"""
Telegram Bot Admin Handlers for Shared API Management

This module provides simplified, intuitive Telegram bot handlers for managing
API configurations using the shared API system.
"""

import logging
from typing import Dict, Any, List, Optional
import json

from aiogram import Router, F
from aiogram.types import (
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from admin.services.shared_api_admin_service import get_shared_api_admin_service
from admin.ui.api_management_ui import APIManagementUI
from utils.decorators import admin_required, error_handler
from middleware import attach_common_middlewares

logger = logging.getLogger(__name__)

router = Router()
attach_common_middlewares(router)


class APIManagementStates(StatesGroup):
    """States for API management workflows"""
    
    # Creation workflow
    waiting_for_name = State()
    waiting_for_base_url = State()
    waiting_for_auth_type = State()
    waiting_for_auth_data = State()
    waiting_for_endpoints = State()
    
    # Editing workflow
    editing_base_url = State()
    editing_auth = State()
    editing_endpoints = State()
    
    # Testing workflow
    selecting_test_endpoint = State()


class APIManagementHandlers:
    """Handlers for API management through Telegram bot interface"""
    
    def __init__(self):
        self.admin_service = get_shared_api_admin_service()
        self.ui = APIManagementUI()
    
    @admin_required
    @error_handler
    async def cmd_api_management(self, message: Message, state: FSMContext):
        """Main API management command"""
        await state.clear()
        
        # Get API configurations summary
        api_configs = await self.admin_service.list_api_configurations()
        
        text = self.ui.format_main_menu(api_configs)
        keyboard = self.ui.create_main_menu_keyboard()
        
        await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_api_main(self, callback: CallbackQuery, state: FSMContext):
        """Main API management menu"""
        await state.clear()
        
        api_configs = await self.admin_service.list_api_configurations()
        
        text = self.ui.format_main_menu(api_configs)
        keyboard = self.ui.create_main_menu_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()
    
    @admin_required
    @error_handler
    async def callback_api_list(self, callback: CallbackQuery, state: FSMContext):
        """List all API configurations"""
        environment = None
        if ":" in callback.data:
            environment = callback.data.split(":", 1)[1]
        
        api_configs = await self.admin_service.list_api_configurations(environment=environment)
        
        text = self.ui.format_api_list(api_configs, environment)
        keyboard = self.ui.create_api_list_keyboard(api_configs, environment)
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()
    
    @admin_required
    @error_handler
    async def callback_api_create(self, callback: CallbackQuery, state: FSMContext):
        """Start API creation workflow"""
        await state.set_state(APIManagementStates.waiting_for_name)
        
        text = self.ui.format_create_api_step1()
        keyboard = self.ui.create_cancel_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer("🆕 Starting API creation...")
    
    @admin_required
    @error_handler
    async def handle_api_name_input(self, message: Message, state: FSMContext):
        """Handle API name input"""
        api_name = message.text.strip()
        
        # Validate name
        if not api_name or len(api_name) < 2:
            await message.answer("❌ API name must be at least 2 characters long. Please try again:")
            return
        
        if not api_name.replace("_", "").replace("-", "").isalnum():
            await message.answer("❌ API name can only contain letters, numbers, hyphens, and underscores. Please try again:")
            return
        
        # Check if name already exists
        existing = await self.admin_service.admin_storage.get_api_config(api_name)
        if existing:
            await message.answer(f"❌ API configuration '{api_name}' already exists. Please choose a different name:")
            return
        
        # Store name and move to next step
        await state.update_data(api_name=api_name)
        await state.set_state(APIManagementStates.waiting_for_base_url)
        
        text = self.ui.format_create_api_step2(api_name)
        keyboard = self.ui.create_cancel_keyboard()
        
        await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def handle_base_url_input(self, message: Message, state: FSMContext):
        """Handle base URL input"""
        base_url = message.text.strip()
        
        # Validate URL
        if not base_url.startswith(("http://", "https://")):
            await message.answer("❌ Base URL must start with http:// or https://. Please try again:")
            return
        
        # Store URL and move to next step
        data = await state.get_data()
        await state.update_data(base_url=base_url)
        
        text = self.ui.format_create_api_step3(data["api_name"], base_url)
        keyboard = self.ui.create_auth_type_keyboard()
        
        await message.answer(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_auth_type_select(self, callback: CallbackQuery, state: FSMContext):
        """Handle authentication type selection"""
        auth_type = callback.data.split(":", 1)[1]
        
        data = await state.get_data()
        await state.update_data(auth_type=auth_type)
        
        if auth_type == "none":
            # No authentication needed, proceed to endpoints
            await self._proceed_to_endpoints(callback, state)
        else:
            # Need authentication data
            await state.set_state(APIManagementStates.waiting_for_auth_data)
            
            text = self.ui.format_auth_data_input(auth_type)
            keyboard = self.ui.create_cancel_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        
        await callback.answer()
    
    async def _proceed_to_endpoints(self, callback: CallbackQuery, state: FSMContext):
        """Proceed to endpoints configuration"""
        data = await state.get_data()
        
        text = self.ui.format_create_api_step4(data["api_name"])
        keyboard = self.ui.create_endpoints_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_skip_endpoints(self, callback: CallbackQuery, state: FSMContext):
        """Skip endpoints configuration and create API"""
        await self._create_api_configuration(callback, state)
    
    @admin_required
    @error_handler
    async def callback_add_basic_endpoints(self, callback: CallbackQuery, state: FSMContext):
        """Add basic endpoints and create API"""
        # Add basic CRUD endpoints
        basic_endpoints = {
            "list": {"path": "/list", "method": "GET", "description": "List items"},
            "get": {"path": "/get", "method": "GET", "description": "Get item"},
            "create": {"path": "/create", "method": "POST", "description": "Create item"},
            "update": {"path": "/update", "method": "PUT", "description": "Update item"},
            "delete": {"path": "/delete", "method": "DELETE", "description": "Delete item"}
        }
        
        await state.update_data(endpoints=basic_endpoints)
        await self._create_api_configuration(callback, state)
    
    async def _create_api_configuration(self, callback: CallbackQuery, state: FSMContext):
        """Create the API configuration"""
        try:
            data = await state.get_data()
            user_id = str(callback.from_user.id)
            
            # Prepare authentication data
            auth_data = {}
            auth_type = data.get("auth_type", "none")
            
            if auth_type != "none" and "auth_data" in data:
                auth_data = data["auth_data"]
            
            # Create the configuration
            success, message, admin_config = await self.admin_service.create_api_configuration(
                name=data["api_name"],
                base_url=data["base_url"],
                created_by=user_id,
                display_name=data.get("display_name", data["api_name"]),
                description=data.get("description", ""),
                auth_type=auth_type,
                auth_data=auth_data,
                endpoints=data.get("endpoints", {}),
                environment="development"
            )
            
            if success:
                # Show success message with options
                text = self.ui.format_creation_success(data["api_name"], message)
                keyboard = self.ui.create_post_creation_keyboard(data["api_name"])
                
                await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
                await callback.answer("✅ API created successfully!")
            else:
                # Show error message
                text = self.ui.format_creation_error(message)
                keyboard = self.ui.create_retry_keyboard()
                
                await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
                await callback.answer("❌ Creation failed", show_alert=True)
            
            await state.clear()
            
        except Exception as e:
            logger.error(f"Failed to create API configuration: {e}")
            await callback.answer("❌ An error occurred", show_alert=True)
            await state.clear()
    
    @admin_required
    @error_handler
    async def callback_api_view(self, callback: CallbackQuery, state: FSMContext):
        """View API configuration details"""
        api_name = callback.data.split(":", 1)[1]
        
        api_details = await self.admin_service.get_api_details(api_name)
        if not api_details:
            await callback.answer("❌ API configuration not found", show_alert=True)
            return
        
        text = self.ui.format_api_details(api_details)
        keyboard = self.ui.create_api_details_keyboard(api_name)
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()
    
    @admin_required
    @error_handler
    async def callback_api_test(self, callback: CallbackQuery, state: FSMContext):
        """Test API configuration"""
        api_name = callback.data.split(":", 1)[1]
        
        await callback.answer("🧪 Testing API connection...")
        
        # Perform the test
        success, message, test_results = await self.admin_service.test_api_connection(api_name)
        
        # Format and display results
        text = self.ui.format_test_results(api_name, success, message, test_results)
        keyboard = self.ui.create_test_results_keyboard(api_name, success)
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_api_test_endpoint(self, callback: CallbackQuery, state: FSMContext):
        """Test specific API endpoint"""
        parts = callback.data.split(":", 2)
        api_name = parts[1]
        endpoint_name = parts[2]
        
        await callback.answer(f"🧪 Testing endpoint '{endpoint_name}'...")
        
        # Perform the test
        success, message, test_results = await self.admin_service.test_api_connection(
            api_name, endpoint_name
        )
        
        # Format and display results
        text = self.ui.format_endpoint_test_results(api_name, endpoint_name, success, message, test_results)
        keyboard = self.ui.create_test_results_keyboard(api_name, success)
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_api_health_check_all(self, callback: CallbackQuery, state: FSMContext):
        """Check health of all APIs"""
        await callback.answer("🔍 Checking health of all APIs...")
        
        api_configs = await self.admin_service.list_api_configurations(enabled_only=True)
        
        health_results = []
        for config in api_configs:
            success, message, test_results = await self.admin_service.test_api_connection(config["name"])
            health_results.append({
                "name": config["name"],
                "success": success,
                "message": message,
                "test_results": test_results
            })
        
        text = self.ui.format_health_check_results(health_results)
        keyboard = self.ui.create_health_check_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
    
    @admin_required
    @error_handler
    async def callback_cancel(self, callback: CallbackQuery, state: FSMContext):
        """Cancel current operation"""
        await state.clear()
        await self.callback_api_main(callback, state)


_handlers = APIManagementHandlers()

# Command handlers
router.message.register(_handlers.cmd_api_management, Command("api"))

# Callback handlers
router.callback_query.register(_handlers.callback_api_main, F.data == "api_main")
router.callback_query.register(_handlers.callback_api_list, F.data.startswith("api_list"))
router.callback_query.register(_handlers.callback_api_create, F.data == "api_create")
router.callback_query.register(_handlers.callback_auth_type_select, F.data.startswith("auth_type:"))
router.callback_query.register(_handlers.callback_skip_endpoints, F.data == "skip_endpoints")
router.callback_query.register(_handlers.callback_add_basic_endpoints, F.data == "add_basic_endpoints")
router.callback_query.register(_handlers.callback_api_view, F.data.startswith("api_view:"))
router.callback_query.register(_handlers.callback_api_test, F.data.startswith("api_test:"))
router.callback_query.register(_handlers.callback_api_test_endpoint, F.data.startswith("api_test_endpoint:"))
router.callback_query.register(_handlers.callback_api_health_check_all, F.data == "api_health_check_all")
router.callback_query.register(_handlers.callback_cancel, F.data == "cancel")

# State handlers
router.message.register(_handlers.handle_api_name_input, APIManagementStates.waiting_for_name)
router.message.register(_handlers.handle_base_url_input, APIManagementStates.waiting_for_base_url)
