"""
API Configuration Classes

Provides flexible configuration classes for defining API client behavior,
endpoints, authentication, and other settings.
"""

import re
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime

from ..core.interfaces import BaseAPIConfiguration
from ..core.constants import (
    HTT<PERSON>ethod, 
    AuthenticationType,
    DEFAULT_TIMEOUT,
    DEFAULT_MAX_RETRIES,
    DEFAULT_RETRY_DELAY,
    URL_PATTERN,
    COMMON_HEADERS,
)
from ..core.exceptions import ConfigurationError, ValidationError


@dataclass
class TimeoutConfiguration:
    """Configuration for request timeouts"""
    connect: int = 10
    read: int = 30
    total: int = 60
    
    def __post_init__(self):
        if self.connect <= 0 or self.read <= 0 or self.total <= 0:
            raise ValidationError("Timeout values must be positive")
        if self.total < max(self.connect, self.read):
            raise ValidationError("Total timeout must be >= connect and read timeouts")


@dataclass
class RetryConfiguration:
    """Configuration for request retries"""
    max_attempts: int = DEFAULT_MAX_RETRIES
    delay: float = DEFAULT_RETRY_DELAY
    backoff_factor: float = 1.0
    retry_on_status: List[int] = field(default_factory=lambda: [500, 502, 503, 504])
    
    def __post_init__(self):
        if self.max_attempts < 0:
            raise ValidationError("Max retry attempts cannot be negative")
        if self.delay < 0:
            raise ValidationError("Retry delay cannot be negative")
        if self.backoff_factor < 0:
            raise ValidationError("Backoff factor cannot be negative")


@dataclass
class AuthenticationConfiguration:
    """Configuration for API authentication"""
    type: AuthenticationType = AuthenticationType.NONE
    api_key: Optional[str] = None
    api_key_header: str = "X-API-Key"
    bearer_token: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    custom_headers: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        self._validate_auth_config()
    
    def _validate_auth_config(self):
        """Validate authentication configuration"""
        if self.type == AuthenticationType.API_KEY and not self.api_key:
            raise ValidationError("API key is required for API key authentication")
        elif self.type == AuthenticationType.BEARER_TOKEN and not self.bearer_token:
            raise ValidationError("Bearer token is required for bearer token authentication")
        elif self.type == AuthenticationType.BASIC_AUTH:
            if not self.username or not self.password:
                raise ValidationError("Username and password are required for basic authentication")
    
    def get_headers(self) -> Dict[str, str]:
        """Get authentication headers"""
        headers = {}
        
        if self.type == AuthenticationType.API_KEY and self.api_key:
            headers[self.api_key_header] = self.api_key
        elif self.type == AuthenticationType.BEARER_TOKEN and self.bearer_token:
            headers["Authorization"] = f"Bearer {self.bearer_token}"
        elif self.type == AuthenticationType.BASIC_AUTH and self.username and self.password:
            import base64
            credentials = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
            headers["Authorization"] = f"Basic {credentials}"
        elif self.type == AuthenticationType.CUSTOM_HEADER:
            headers.update(self.custom_headers)
        
        return headers


@dataclass
class EndpointConfiguration:
    """Configuration for a specific API endpoint"""
    name: str
    path: str
    method: HTTPMethod = HTTPMethod.GET
    timeout: Optional[TimeoutConfiguration] = None
    custom_headers: Dict[str, str] = field(default_factory=dict)
    description: Optional[str] = None
    
    def __post_init__(self):
        if not self.path.startswith('/'):
            self.path = '/' + self.path
        
        if not self.name:
            raise ValidationError("Endpoint name cannot be empty")


class APIConfiguration(BaseAPIConfiguration):
    """Complete API configuration for a specific API"""
    
    def __init__(
        self,
        name: str,
        base_url: str,
        endpoints: Optional[Dict[str, EndpointConfiguration]] = None,
        authentication: Optional[AuthenticationConfiguration] = None,
        default_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[TimeoutConfiguration] = None,
        retry: Optional[RetryConfiguration] = None,
        description: Optional[str] = None,
        version: str = "1.0",
        environment: str = "development",
        **kwargs
    ):
        # Initialize base configuration
        super().__init__(
            name=name,
            base_url=base_url,
            authentication=authentication.get_headers() if authentication else {},
            default_headers=default_headers or {},
            timeout=timeout.total if timeout else DEFAULT_TIMEOUT,
            max_retries=retry.max_attempts if retry else DEFAULT_MAX_RETRIES,
            **kwargs
        )
        
        # Store detailed configurations
        self.endpoints = endpoints or {}
        self.auth_config = authentication or AuthenticationConfiguration()
        self.timeout_config = timeout or TimeoutConfiguration()
        self.retry_config = retry or RetryConfiguration()
        self.description = description
        self.version = version
        self.environment = environment
        
        # Merge default headers with common headers
        merged_headers = COMMON_HEADERS.copy()
        merged_headers.update(self.default_headers)
        self.default_headers = merged_headers
        
        # Validate configuration
        self.validate()
    
    def get_endpoint_url(self, endpoint_name: str) -> str:
        """Get the full URL for a specific endpoint"""
        if endpoint_name not in self.endpoints:
            raise ConfigurationError(f"Endpoint '{endpoint_name}' not found in configuration")
        
        endpoint = self.endpoints[endpoint_name]
        return f"{self.base_url}{endpoint.path}"
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for requests"""
        return self.auth_config.get_headers()
    
    def validate(self) -> bool:
        """Validate the configuration"""
        # Validate base URL
        if not re.match(URL_PATTERN, self.base_url):
            raise ValidationError(f"Invalid base URL format: {self.base_url}")
        
        # Validate name
        if not self.name or len(self.name.strip()) < 2:
            raise ValidationError("API name must be at least 2 characters long")
        
        # Validate endpoints
        for endpoint_name, endpoint in self.endpoints.items():
            if not isinstance(endpoint, EndpointConfiguration):
                raise ValidationError(f"Invalid endpoint configuration for '{endpoint_name}'")
        
        return True
    
    def add_endpoint(self, endpoint: EndpointConfiguration) -> None:
        """Add a new endpoint to the configuration"""
        self.endpoints[endpoint.name] = endpoint
    
    def remove_endpoint(self, endpoint_name: str) -> None:
        """Remove an endpoint from the configuration"""
        if endpoint_name in self.endpoints:
            del self.endpoints[endpoint_name]
    
    def get_endpoint_timeout(self, endpoint_name: str) -> TimeoutConfiguration:
        """Get timeout configuration for a specific endpoint"""
        if endpoint_name in self.endpoints:
            endpoint = self.endpoints[endpoint_name]
            if endpoint.timeout:
                return endpoint.timeout
        return self.timeout_config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "name": self.name,
            "base_url": self.base_url,
            "description": self.description,
            "version": self.version,
            "environment": self.environment,
            "endpoints": {
                name: {
                    "name": ep.name,
                    "path": ep.path,
                    "method": ep.method.value,
                    "description": ep.description,
                    "custom_headers": ep.custom_headers,
                }
                for name, ep in self.endpoints.items()
            },
            "authentication": {
                "type": self.auth_config.type.value,
                "api_key_header": self.auth_config.api_key_header,
                "custom_headers": self.auth_config.custom_headers,
            },
            "default_headers": self.default_headers,
            "timeout": {
                "connect": self.timeout_config.connect,
                "read": self.timeout_config.read,
                "total": self.timeout_config.total,
            },
            "retry": {
                "max_attempts": self.retry_config.max_attempts,
                "delay": self.retry_config.delay,
                "backoff_factor": self.retry_config.backoff_factor,
                "retry_on_status": self.retry_config.retry_on_status,
            },
            "created_at": self.created_at.isoformat(),
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "APIConfiguration":
        """Create configuration from dictionary"""
        # Parse endpoints
        endpoints = {}
        for name, ep_data in data.get("endpoints", {}).items():
            endpoints[name] = EndpointConfiguration(
                name=ep_data["name"],
                path=ep_data["path"],
                method=HTTPMethod(ep_data["method"]),
                description=ep_data.get("description"),
                custom_headers=ep_data.get("custom_headers", {}),
            )
        
        # Parse authentication
        auth_data = data.get("authentication", {})
        auth = AuthenticationConfiguration(
            type=AuthenticationType(auth_data.get("type", "none")),
            api_key_header=auth_data.get("api_key_header", "X-API-Key"),
            custom_headers=auth_data.get("custom_headers", {}),
        )
        
        # Parse timeout
        timeout_data = data.get("timeout", {})
        timeout = TimeoutConfiguration(
            connect=timeout_data.get("connect", 10),
            read=timeout_data.get("read", 30),
            total=timeout_data.get("total", 60),
        )
        
        # Parse retry
        retry_data = data.get("retry", {})
        retry = RetryConfiguration(
            max_attempts=retry_data.get("max_attempts", 3),
            delay=retry_data.get("delay", 1.0),
            backoff_factor=retry_data.get("backoff_factor", 1.0),
            retry_on_status=retry_data.get("retry_on_status", [500, 502, 503, 504]),
        )
        
        return cls(
            name=data["name"],
            base_url=data["base_url"],
            endpoints=endpoints,
            authentication=auth,
            default_headers=data.get("default_headers", {}),
            timeout=timeout,
            retry=retry,
            description=data.get("description"),
            version=data.get("version", "1.0"),
            environment=data.get("environment", "development"),
        )
